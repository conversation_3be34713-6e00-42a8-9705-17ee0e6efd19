(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0df025"],{"87a1":function(t,e,a){"use strict";var i=a("aa74"),n=a("22b4"),r=(a("1be7"),a("f95e")),o=a("5e81"),l=a("ee29");Object(n["a"])([r["a"],o["a"]]);Object(n["a"])(l["a"]),i["t"]({type:"series.wordCloud",visualStyleAccessPath:"textStyle",visualStyleMapper:function(t){return{fill:t.get("color")}},visualDrawType:"fill",optionUpdated:function(){var t=this.option;t.gridSize=Math.max(Math.floor(t.gridSize),4)},getInitialData:function(t,e){var a=i["A"].createDimensions(t.data,{coordDimensions:["value"]}),n=new i["e"](a,this);return n.initData(t.data),n},defaultOption:{maskImage:null,shape:"circle",keepAspect:!1,left:"center",top:"center",width:"70%",height:"80%",sizeRange:[12,60],rotationRange:[-90,90],rotationStep:45,gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,textStyle:{fontWeight:"normal"}}}),i["q"]({type:"wordCloud",render:function(t,e,a){var n=this.group;n.removeAll();var r=t.getData(),o=t.get("gridSize");t.layoutInstance.ondraw=function(e,a,l,s){var d=r.getItemModel(l),h=d.getModel("textStyle"),u=new i["z"].Text({style:i["A"].createTextStyle(h),scaleX:1/s.info.mu,scaleY:1/s.info.mu,x:(s.gx+s.info.gw/2)*o,y:(s.gy+s.info.gh/2)*o,rotation:s.rot});u.setStyle({x:s.info.fillTextOffsetX,y:s.info.fillTextOffsetY+.5*a,text:e,verticalAlign:"middle",fill:r.getItemVisual(l,"style").fill,fontSize:a}),n.add(u),r.setItemGraphicEl(l,u),u.ensureState("emphasis").style=i["A"].createTextStyle(d.getModel(["emphasis","textStyle"]),{state:"emphasis"}),u.ensureState("blur").style=i["A"].createTextStyle(d.getModel(["blur","textStyle"]),{state:"blur"}),i["A"].enableHoverEmphasis(u,d.get(["emphasis","focus"]),d.get(["emphasis","blurScope"])),u.stateTransition={duration:t.get("animation")?t.get(["stateAnimation","duration"]):0,easing:t.get(["stateAnimation","easing"])},u.__highDownDispatcher=!0},this._model=t},remove:function(){this.group.removeAll(),this._model.layoutInstance.dispose()},dispose:function(){this._model.layoutInstance.dispose()}}),
/*!
 * wordcloud2.js
 * http://timdream.org/wordcloud2.js/
 *
 * Copyright 2011 - 2019 Tim Guan-tin Chien and contributors.
 * Released under the MIT license
 */
window.setImmediate||(window.setImmediate=function(){return window.msSetImmediate||window.webkitSetImmediate||window.mozSetImmediate||window.oSetImmediate||function(){if(!window.postMessage||!window.addEventListener)return null;var t=[void 0],e="zero-timeout-message",a=function(a){var i=t.length;return t.push(a),window.postMessage(e+i.toString(36),"*"),i};return window.addEventListener("message",(function(a){if("string"===typeof a.data&&a.data.substr(0,e.length)===e){a.stopImmediatePropagation();var i=parseInt(a.data.substr(e.length),36);t[i]&&(t[i](),t[i]=void 0)}}),!0),window.clearImmediate=function(e){t[e]&&(t[e]=void 0)},a}()||function(t){window.setTimeout(t,0)}}()),window.clearImmediate||(window.clearImmediate=function(){return window.msClearImmediate||window.webkitClearImmediate||window.mozClearImmediate||window.oClearImmediate||function(t){window.clearTimeout(t)}}());var s=function(){var t=document.createElement("canvas");if(!t||!t.getContext)return!1;var e=t.getContext("2d");return!!e&&(!!e.getImageData&&(!!e.fillText&&(!!Array.prototype.some&&!!Array.prototype.push)))}(),d=function(){if(s){var t,e,a=document.createElement("canvas").getContext("2d"),i=20;while(i){if(a.font=i.toString(10)+"px sans-serif",a.measureText("Ｗ").width===t&&a.measureText("m").width===e)return i+1;t=a.measureText("Ｗ").width,e=a.measureText("m").width,i--}return 0}}(),h=function(t){if(Array.isArray(t)){var e=t.slice();return e.splice(0,2),e}return[]},u=function(t){for(var e,a,i=t.length;i;)e=Math.floor(Math.random()*i),a=t[--i],t[i]=t[e],t[e]=a;return t},c={},f=function(t,e){if(s){var a=Math.floor(Math.random()*Date.now());Array.isArray(t)||(t=[t]),t.forEach((function(e,a){if("string"===typeof e){if(t[a]=document.getElementById(e),!t[a])throw new Error("The element id specified is not found.")}else if(!e.tagName&&!e.appendChild)throw new Error("You must pass valid HTML elements, or ID of the element.")}));var i={list:[],fontFamily:'"Trebuchet MS", "Heiti TC", "微軟正黑體", "Arial Unicode MS", "Droid Fallback Sans", sans-serif',fontWeight:"normal",color:"random-dark",minSize:0,weightFactor:1,clearCanvas:!0,backgroundColor:"#fff",gridSize:8,drawOutOfBound:!1,shrinkToFit:!1,origin:null,drawMask:!1,maskColor:"rgba(255,0,0,0.3)",maskGapWidth:.3,layoutAnimation:!0,wait:0,abortThreshold:0,abort:function(){},minRotation:-Math.PI/2,maxRotation:Math.PI/2,rotationStep:.1,shuffle:!0,rotateRatio:.1,shape:"circle",ellipticity:.65,classes:null,hover:null,click:null};if(e)for(var n in e)n in i&&(i[n]=e[n]);if("function"!==typeof i.weightFactor){var r=i.weightFactor;i.weightFactor=function(t){return t*r}}if("function"!==typeof i.shape)switch(i.shape){case"circle":default:i.shape="circle";break;case"cardioid":i.shape=function(t){return 1-Math.sin(t)};break;case"diamond":i.shape=function(t){var e=t%(2*Math.PI/4);return 1/(Math.cos(e)+Math.sin(e))};break;case"square":i.shape=function(t){return Math.min(1/Math.abs(Math.cos(t)),1/Math.abs(Math.sin(t)))};break;case"triangle-forward":i.shape=function(t){var e=t%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"triangle":case"triangle-upright":i.shape=function(t){var e=(t+3*Math.PI/2)%(2*Math.PI/3);return 1/(Math.cos(e)+Math.sqrt(3)*Math.sin(e))};break;case"pentagon":i.shape=function(t){var e=(t+.955)%(2*Math.PI/5);return 1/(Math.cos(e)+.726543*Math.sin(e))};break;case"star":i.shape=function(t){var e=(t+.955)%(2*Math.PI/10);return(t+.955)%(2*Math.PI/5)-2*Math.PI/10>=0?1/(Math.cos(2*Math.PI/10-e)+3.07768*Math.sin(2*Math.PI/10-e)):1/(Math.cos(e)+3.07768*Math.sin(e))};break}i.gridSize=Math.max(Math.floor(i.gridSize),4);var o,l,f,g,m,w,v,p,y=i.gridSize,x=y-i.maskGapWidth,M=Math.abs(i.maxRotation-i.minRotation),b=Math.min(i.maxRotation,i.minRotation),S=i.rotationStep;switch(i.color){case"random-dark":v=function(){return _(10,50)};break;case"random-light":v=function(){return _(50,90)};break;default:"function"===typeof i.color&&(v=i.color);break}"function"===typeof i.fontWeight&&(p=i.fontWeight);var I=null;"function"===typeof i.classes&&(I=i.classes);var T,k=!1,C=[],E=function(t){var e,a,i=t.currentTarget,n=i.getBoundingClientRect();t.touches?(e=t.touches[0].clientX,a=t.touches[0].clientY):(e=t.clientX,a=t.clientY);var r=e-n.left,o=a-n.top,l=Math.floor(r*(i.width/n.width||1)/y),s=Math.floor(o*(i.height/n.height||1)/y);return C[l]?C[l][s]:null},A=function(t){var e=E(t);T!==e&&(T=e,e?i.hover(e.item,e.dimension,t):i.hover(void 0,void 0,t))},O=function(t){var e=E(t);e&&(i.click(e.item,e.dimension,t),t.preventDefault())},R=[],F=function(t){if(R[t])return R[t];var e=8*t,a=e,n=[];0===t&&n.push([g[0],g[1],0]);while(a--){var r=1;"circle"!==i.shape&&(r=i.shape(a/e*2*Math.PI)),n.push([g[0]+t*r*Math.cos(-a/e*2*Math.PI),g[1]+t*r*Math.sin(-a/e*2*Math.PI)*i.ellipticity,a/e*2*Math.PI])}return R[t]=n,n},D=function(){return i.abortThreshold>0&&(new Date).getTime()-w>i.abortThreshold},z=function(){return 0===i.rotateRatio||Math.random()>i.rotateRatio?0:0===M?b:b+Math.round(Math.random()*M/S)*S},P=function(t,e,a,n){var r=!1,o=i.weightFactor(e);if(o<=i.minSize)return!1;var l,s=1;o<d&&(s=function(){var t=2;while(t*o<d)t+=2;return t}()),l=p?p(t,e,o,n):i.fontWeight;var h=document.createElement("canvas"),u=h.getContext("2d",{willReadFrequently:!0});u.font=l+" "+(o*s).toString(10)+"px "+i.fontFamily;var c=u.measureText(t).width/s,f=Math.max(o*s,u.measureText("m").width,u.measureText("Ｗ").width)/s,g=c+2*f,m=3*f,w=Math.ceil(g/y),v=Math.ceil(m/y);g=w*y,m=v*y;var x=-c/2,M=.4*-f,b=Math.ceil((g*Math.abs(Math.sin(a))+m*Math.abs(Math.cos(a)))/y),S=Math.ceil((g*Math.abs(Math.cos(a))+m*Math.abs(Math.sin(a)))/y),I=S*y,T=b*y;h.setAttribute("width",I),h.setAttribute("height",T),r&&(document.body.appendChild(h),u.save()),u.scale(1/s,1/s),u.translate(I*s/2,T*s/2),u.rotate(-a),u.font=l+" "+(o*s).toString(10)+"px "+i.fontFamily,u.fillStyle="#000",u.textBaseline="middle",u.fillText(t,x*s,(M+.5*o)*s);var k=u.getImageData(0,0,I,T).data;if(D())return!1;r&&(u.strokeRect(x*s,M,c*s,f*s),u.restore());var C,E,A,O=[],R=S,F=[b/2,S/2,b/2,S/2];while(R--){C=b;while(C--){A=y;t:while(A--){E=y;while(E--)if(k[4*((C*y+A)*I+(R*y+E))+3]){O.push([R,C]),R<F[3]&&(F[3]=R),R>F[1]&&(F[1]=R),C<F[0]&&(F[0]=C),C>F[2]&&(F[2]=C),r&&(u.fillStyle="rgba(255, 0, 0, 0.5)",u.fillRect(R*y,C*y,y-.5,y-.5));break t}}r&&(u.fillStyle="rgba(0, 0, 255, 0.5)",u.fillRect(R*y,C*y,y-.5,y-.5))}}return r&&(u.fillStyle="rgba(0, 255, 0, 0.5)",u.fillRect(F[3]*y,F[0]*y,(F[1]-F[3]+1)*y,(F[2]-F[0]+1)*y)),{mu:s,occupied:O,bounds:F,gw:S,gh:b,fillTextOffsetX:x,fillTextOffsetY:M,fillTextWidth:c,fillTextHeight:f,fontSize:o}},L=function(t,e,a,n,r){var s=r.length;while(s--){var d=t+r[s][0],h=e+r[s][1];if(d>=l||h>=f||d<0||h<0){if(!i.drawOutOfBound)return!1}else if(!o[d][h])return!1}return!0},W=function(e,a,n,r,o,l,s,d,h,u){var c,f,g,m=n.fontSize;c=v?v(r,o,m,l,s,u):i.color,f=p?p(r,o,m,u):i.fontWeight,g=I?I(r,o,m,u):i.classes,t.forEach((function(t){if(t.getContext){var o=t.getContext("2d"),l=n.mu;o.save(),o.scale(1/l,1/l),o.font=f+" "+(m*l).toString(10)+"px "+i.fontFamily,o.fillStyle=c,o.translate((e+n.gw/2)*y*l,(a+n.gh/2)*y*l),0!==d&&o.rotate(-d),o.textBaseline="middle",o.fillText(r,n.fillTextOffsetX*l,(n.fillTextOffsetY+.5*m)*l),o.restore()}else{var s=document.createElement("span"),u="";u="rotate("+-d/Math.PI*180+"deg) ",1!==n.mu&&(u+="translateX(-"+n.fillTextWidth/4+"px) scale("+1/n.mu+")");var w={position:"absolute",display:"block",font:f+" "+m*n.mu+"px "+i.fontFamily,left:(e+n.gw/2)*y+n.fillTextOffsetX+"px",top:(a+n.gh/2)*y+n.fillTextOffsetY+"px",width:n.fillTextWidth+"px",height:n.fillTextHeight+"px",lineHeight:m+"px",whiteSpace:"nowrap",transform:u,webkitTransform:u,msTransform:u,transformOrigin:"50% 40%",webkitTransformOrigin:"50% 40%",msTransformOrigin:"50% 40%"};for(var v in c&&(w.color=c),s.textContent=r,w)s.style[v]=w[v];if(h)for(var p in h)s.setAttribute(p,h[p]);g&&(s.className+=g),t.appendChild(s)}}))},B=function(e,a,i,n,r){if(!(e>=l||a>=f||e<0||a<0)){if(o[e][a]=!1,i){var s=t[0].getContext("2d");s.fillRect(e*y,a*y,x,x)}k&&(C[e][a]={item:r,dimension:n})}},X=function(e,a,n,r,o,s){var d,h,u=o.occupied,c=i.drawMask;if(c&&(d=t[0].getContext("2d"),d.save(),d.fillStyle=i.maskColor),k){var g=o.bounds;h={x:(e+g[3])*y,y:(a+g[0])*y,w:(g[1]-g[3]+1)*y,h:(g[2]-g[0]+1)*y}}var m=u.length;while(m--){var w=e+u[m][0],v=a+u[m][1];w>=l||v>=f||w<0||v<0||B(w,v,c,h,s)}c&&d.restore()},Y=function t(e,a){if(a>20)return null;var n,r,o;Array.isArray(e)?(n=e[0],r=e[1]):(n=e.word,r=e.weight,o=e.attributes);var s=z(),d=h(e),c=P(n,r,s,d);if(!c)return!1;if(D())return!1;if(!i.drawOutOfBound&&!i.shrinkToFit){var g=c.bounds;if(g[1]-g[3]+1>l||g[2]-g[0]+1>f)return!1}var w=m+1,v=function(t){var a=Math.floor(t[0]-c.gw/2),i=Math.floor(t[1]-c.gh/2),l=c.gw,h=c.gh;return!!L(a,i,l,h,c.occupied)&&(W(a,i,c,n,r,m-w,t[2],s,o,d),X(a,i,l,h,c,e),{gx:a,gy:i,rot:s,info:c})};while(w--){var p=F(m-w);i.shuffle&&(p=[].concat(p),u(p));for(var y=0;y<p.length;y++){var x=v(p[y]);if(x)return x}}return i.shrinkToFit?(Array.isArray(e)?e[1]=3*e[1]/4:e.weight=3*e.weight/4,t(e,a+1)):null},H=function(e,a,i){if(a)return!t.some((function(t){var a=new CustomEvent(e,{detail:i||{}});return!t.dispatchEvent(a)}),this);t.forEach((function(t){var a=new CustomEvent(e,{detail:i||{}});t.dispatchEvent(a)}),this)},q=function(){var e=t[0];if(e.getContext)l=Math.ceil(e.width/y),f=Math.ceil(e.height/y);else{var n=e.getBoundingClientRect();l=Math.ceil(n.width/y),f=Math.ceil(n.height/y)}if(H("wordcloudstart",!0)){var r,s,d,h,u;if(g=i.origin?[i.origin[0]/y,i.origin[1]/y]:[l/2,f/2],m=Math.floor(Math.sqrt(l*l+f*f)),o=[],!e.getContext||i.clearCanvas){t.forEach((function(t){if(t.getContext){var e=t.getContext("2d");e.fillStyle=i.backgroundColor,e.clearRect(0,0,l*(y+1),f*(y+1)),e.fillRect(0,0,l*(y+1),f*(y+1))}else t.textContent="",t.style.backgroundColor=i.backgroundColor,t.style.position="relative"})),r=l;while(r--){o[r]=[],s=f;while(s--)o[r][s]=!0}}else{var v=document.createElement("canvas").getContext("2d");v.fillStyle=i.backgroundColor,v.fillRect(0,0,1,1);var p,x,M=v.getImageData(0,0,1,1).data,b=e.getContext("2d").getImageData(0,0,l*y,f*y).data;r=l;while(r--){o[r]=[],s=f;while(s--){x=y;t:while(x--){p=y;while(p--){d=4;while(d--)if(b[4*((s*y+x)*l*y+(r*y+p))+d]!==M[d]){o[r][s]=!1;break t}}}!1!==o[r][s]&&(o[r][s]=!0)}}b=v=M=void 0}if(i.hover||i.click){k=!0,r=l+1;while(r--)C[r]=[];i.hover&&e.addEventListener("mousemove",A),i.click&&(e.addEventListener("click",O),e.addEventListener("touchstart",O),e.addEventListener("touchend",(function(t){t.preventDefault()})),e.style.webkitTapHighlightColor="rgba(0, 0, 0, 0)"),e.addEventListener("wordcloudstart",(function t(){e.removeEventListener("wordcloudstart",t),e.removeEventListener("mousemove",A),e.removeEventListener("click",O),T=void 0}))}d=0;var S=!0;i.layoutAnimation?0!==i.wait?(h=window.setTimeout,u=window.clearTimeout):(h=window.setImmediate,u=window.clearImmediate):(h=function(t){t()},u=function(){S=!1});var I=function(e,a){t.forEach((function(t){t.addEventListener(e,a)}),this)},E=function(e,a){t.forEach((function(t){t.removeEventListener(e,a)}),this)},R=function t(){E("wordcloudstart",t),u(c[a])};I("wordcloudstart",R),c[a]=(i.layoutAnimation?h:setTimeout)((function t(){if(S){if(d>=i.list.length)return u(c[a]),H("wordcloudstop",!1),E("wordcloudstart",R),void delete c[a];w=(new Date).getTime();var e=Y(i.list[d],0),n=!H("wordclouddrawn",!0,{item:i.list[d],drawn:e});if(D()||n)return u(c[a]),i.abort(),H("wordcloudabort",!1),H("wordcloudstop",!1),void E("wordcloudstart",R);d++,c[a]=h(t,i.wait)}}),i.wait)}};q()}function _(t,e){return"hsl("+(360*Math.random()).toFixed()+","+(30*Math.random()+70).toFixed()+"%,"+(Math.random()*(e-t)+t).toFixed()+"%)"}};f.isSupported=s,f.minFontSize=d;var g=f;if(!g.isSupported)throw new Error("Sorry your browser not support wordCloud");function m(t){for(var e=t.getContext("2d"),a=e.getImageData(0,0,t.width,t.height),i=e.createImageData(a),n=0,r=0,o=0;o<a.data.length;o+=4){var l=a.data[o+3];if(l>128){var s=a.data[o]+a.data[o+1]+a.data[o+2];n+=s,++r}}var d=n/r;for(o=0;o<a.data.length;o+=4){s=a.data[o]+a.data[o+1]+a.data[o+2],l=a.data[o+3];l<128||s>d?(i.data[o]=0,i.data[o+1]=0,i.data[o+2]=0,i.data[o+3]=0):(i.data[o]=255,i.data[o+1]=255,i.data[o+2]=255,i.data[o+3]=255)}e.putImageData(i,0,0)}function w(t,e){var a=t.width,i=t.height;a>i*e?(t.x+=(a-i*e)/2,t.width=i*e):(t.y+=(i-a/e)/2,t.height=a/e)}i["J"]((function(t,e){t.eachSeriesByType("wordCloud",(function(a){var n=i["A"].getLayoutRect(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()}),r=a.get("keepAspect"),o=a.get("maskImage"),l=o?o.width/o.height:1;r&&w(n,l);var s=a.getData(),d=document.createElement("canvas");d.width=n.width,d.height=n.height;var h=d.getContext("2d");if(o)try{h.drawImage(o,0,0,d.width,d.height),m(d)}catch(x){console.error("Invalid mask image"),console.error(x.toString())}var u=a.get("sizeRange"),c=a.get("rotationRange"),f=s.getDataExtent("value"),v=Math.PI/180,p=a.get("gridSize");function y(t){var e=t.detail.item;t.detail.drawn&&a.layoutInstance.ondraw&&(t.detail.drawn.gx+=n.x/p,t.detail.drawn.gy+=n.y/p,a.layoutInstance.ondraw(e[0],e[1],e[2],t.detail.drawn))}g(d,{list:s.mapArray("value",(function(t,e){var a=s.getItemModel(e);return[s.getName(e),a.get("textStyle.fontSize",!0)||i["E"].linearMap(t,f,u),e]})).sort((function(t,e){return e[1]-t[1]})),fontFamily:a.get("textStyle.fontFamily")||a.get("emphasis.textStyle.fontFamily")||t.get("textStyle.fontFamily"),fontWeight:a.get("textStyle.fontWeight")||a.get("emphasis.textStyle.fontWeight")||t.get("textStyle.fontWeight"),gridSize:p,ellipticity:n.height/n.width,minRotation:c[0]*v,maxRotation:c[1]*v,clearCanvas:!o,rotateRatio:1,rotationStep:a.get("rotationStep")*v,drawOutOfBound:a.get("drawOutOfBound"),shrinkToFit:a.get("shrinkToFit"),layoutAnimation:a.get("layoutAnimation"),shuffle:!1,shape:a.get("shape")}),d.addEventListener("wordclouddrawn",y),a.layoutInstance&&a.layoutInstance.dispose(),a.layoutInstance={ondraw:null,dispose:function(){d.removeEventListener("wordclouddrawn",y),d.addEventListener("wordclouddrawn",(function(t){t.preventDefault()}))}}}))})),i["P"]((function(t){var e=(t||{}).series;!i["ab"].isArray(e)&&(e=e?[e]:[]);var a=["shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function n(t){t&&i["ab"].each(a,(function(e){t.hasOwnProperty(e)&&(t["text"+i["u"].capitalFirst(e)]=t[e])}))}i["ab"].each(e,(function(t){if(t&&"wordCloud"===t.type){var e=t.textStyle||{};n(e.normal),n(e.emphasis)}}))}))}}]);