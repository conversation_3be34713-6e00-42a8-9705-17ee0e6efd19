(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-30ba103a"],{"05f52":function(t,e,r){t.exports=r.p+"static/img/singUpSuccess.f99ad898.png"},"265d":function(t,e,r){"use strict";r.d(e,"n",(function(){return o})),r.d(e,"p",(function(){return u})),r.d(e,"q",(function(){return a})),r.d(e,"o",(function(){return i})),r.d(e,"h",(function(){return d})),r.d(e,"m",(function(){return s})),r.d(e,"d",(function(){return c})),r.d(e,"s",(function(){return l})),r.d(e,"g",(function(){return f})),r.d(e,"f",(function(){return m})),r.d(e,"e",(function(){return p})),r.d(e,"r",(function(){return h})),r.d(e,"l",(function(){return b})),r.d(e,"k",(function(){return g})),r.d(e,"a",(function(){return v})),r.d(e,"b",(function(){return j})),r.d(e,"c",(function(){return y})),r.d(e,"i",(function(){return O})),r.d(e,"j",(function(){return q}));var n=r("b775");function o(t){return Object(n["a"])({url:"/drill/task/query",method:"post",data:t})}function u(t){return Object(n["a"])({url:"/drill/task/save",method:"post",data:t})}function a(t){return Object(n["a"])({url:"/drill/task/user",method:"post",data:t})}function i(t){return Object(n["a"])({url:"/drill/task/query/one",method:"post",data:t})}function d(t){return Object(n["a"])({url:"/drill/process/start",method:"post",data:t})}function s(t){return Object(n["a"])({url:"/drill/stage/next",method:"post",data:t})}function c(t){return Object(n["a"])({url:"/drill/comment/publish",method:"post",data:t})}function l(t){return Object(n["a"])({url:"/file/getUrlByIds",method:"post",data:t})}function f(t){return Object(n["a"])({url:"/drill/process/leave",method:"post",data:t})}function m(t){return Object(n["a"])({url:"/drill/process/end",method:"post",data:t})}function p(t){return Object(n["a"])({url:"/drill/comment/query",method:"post",data:t})}function h(t){return Object(n["a"])({url:"/drill/timer/start",method:"post",data:t})}function b(t){return Object(n["a"])({url:"/drill/sign/save",method:"post",data:t})}function g(t){return Object(n["a"])({url:"/drill/sign/query",method:"post",data:t})}function v(t){return Object(n["a"])({url:"/comment/like/add",method:"post",data:t})}function j(t){return Object(n["a"])({url:"/comment/like/cancel",method:"post",data:t})}function y(t){return Object(n["a"])({url:"/comment/reply/add",method:"post",data:t})}function O(t){return Object(n["a"])({url:"/drill/score/query",method:"post",data:t})}function q(t){return Object(n["a"])({url:"/drill/score/statistics",method:"post",data:t})}},3158:function(t,e,r){},"39f8":function(t,e,r){"use strict";r.r(e);var n=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("div",{staticClass:"vsSingUp"},[r("div",{staticClass:"title"},[t._v("扫码签到")]),t.sended?[t._m(0)]:[r("div",{staticClass:"formBox"},[r("el-form",{ref:"form",attrs:{model:t.queryForm,"label-width":"80px",rules:t.rules}},[r("el-form-item",{attrs:{label:"姓名：",prop:"userName"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入您的姓名"},model:{value:t.queryForm.userName,callback:function(e){t.$set(t.queryForm,"userName","string"===typeof e?e.trim():e)},expression:"queryForm.userName"}})],1),r("el-form-item",{attrs:{label:"手机号：",prop:"phone"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入您的手机号"},model:{value:t.queryForm.phone,callback:function(e){t.$set(t.queryForm,"phone","string"===typeof e?e.trim():e)},expression:"queryForm.phone"}})],1)],1)],1),r("div",{staticClass:"buttonGroup"},[r("el-button",{attrs:{type:"primary",loading:t.loading},on:{click:t.onSubmit}},[t._v("提交")])],1)]],2)},o=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"sended"},[n("img",{attrs:{src:r("05f52"),alt:""}}),n("div",[t._v("签到成功！")])])}],u=r("5530"),a=(r("ac1f"),r("00b4"),r("265d")),i={data:function(){return{loading:!1,queryForm:{userName:"",phone:""},rules:{userName:[{required:!0,message:"请输入姓名",trigger:"change"}],phone:[{required:!1,message:"请输入手机号码",trigger:"blur"},{validator:this.validatePhoneNumber,trigger:"change"}]},sended:!1}},computed:{drillTaskId:function(){return this.$route.query.drillTaskId}},created:function(){document.title="扫码签到"},methods:{onSubmit:function(){var t=this;this.$refs.form.validate((function(e){if(e){var r=Object(u["a"])({drillTaskId:t.drillTaskId},t.queryForm);console.log("params",r),t.loading=!0,Object(a["l"])(r).then((function(e){t.$message.success("签到成功"),t.loading=!1,t.sended=!0})).catch((function(e){t.loading=!1}))}}))},validatePhoneNumber:function(t,e,r){if(e){var n=/^1[1-9]\d{9}$/;n.test(e)?r():r(new Error("请输入正确的11位手机号码"))}else r()},handelClose:function(){window.close()}}},d=i,s=(r("a19a"),r("2877")),c=Object(s["a"])(d,n,o,!1,null,"06c7617d",null);e["default"]=c.exports},a19a:function(t,e,r){"use strict";r("3158")}}]);