(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c7b50"],{5285:function(e,o,t){"use strict";t.r(o);var u=function(){var e=this,o=e.$createElement;e._self._c;return e._m(0)},r=[function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",{staticClass:"out"},[t("div",[e._v("页面正在跳转中，请稍后... "),t("i",{staticClass:"el-icon-loading"})])])}],n=t("c7eb"),a=t("1da1"),l=(t("b0c0"),t("5f87")),i=t("7ded"),d={name:"SingleLogin",data:function(){return{loading:!1}},created:function(){this.handleLogin()},methods:{handleLogin:function(){var e=this;return Object(a["a"])(Object(n["a"])().mark((function o(){var t,u,r,a,d,c,s;return Object(n["a"])().wrap((function(o){while(1)switch(o.prev=o.next){case 0:if(Object(l["b"])(),a=decodeURIComponent(e.$route.query.token),d={token:a},null!==(t=e.$route.query)&&void 0!==t&&t.token||null===(u=e.$route.query)||void 0===u||!u.param||null===(r=e.$route.query)||void 0===r||!r.code){o.next=6;break}return o.next=6,Object(i["b"])({code:null===(c=e.$route.query)||void 0===c?void 0:c.code,param:null===(s=e.$route.query)||void 0===s?void 0:s.param}).then((function(e){200==e.code&&(d={token:e.token})}));case 6:e.loading=!0,e.$store.dispatch("LoginBySingle",d).then((function(){e.$store.dispatch("GetRouters").then((function(){var o,t,u,r,n,a,l,i;if("collect"==(null===(o=e.$route.query)||void 0===o?void 0:o.flag))e.$router.push({path:"/system/collect"});else if("case"==(null===(t=e.$route.query)||void 0===t?void 0:t.flag)){var d;e.$router.push({path:"/keyFocusMonitor",query:{data:null===(d=e.$route.query)||void 0===d?void 0:d.data}})}else if("politic"==(null===(u=e.$route.query)||void 0===u?void 0:u.flag)||"plat"==(null===(r=e.$route.query)||void 0===r?void 0:r.flag))e.$router.push({path:"/fullSearch/searchRank",query:{flag:e.$route.query.flag}});else if("1"==(null===(n=e.$route.query)||void 0===n?void 0:n.flag)){var c,s;e.$router.push({path:"/infoDeal",query:{flag:null===(c=e.$route.query)||void 0===c?void 0:c.flag,name:null===(s=e.$route.query)||void 0===s?void 0:s.name}})}else if("2"==(null===(a=e.$route.query)||void 0===a?void 0:a.flag)){var v,f;e.$router.push({path:"/infoSend",query:{flag:null===(v=e.$route.query)||void 0===v?void 0:v.flag,name:null===(f=e.$route.query)||void 0===f?void 0:f.name}})}else if(null!==(l=e.$route.query)&&void 0!==l&&l.param&&null!==(i=e.$route.query)&&void 0!==i&&i.code)e.$router.push({path:"/publicOpinionMonitor"});else{var h=e.$store.state.user.newPath;console.log("homePath :>> ",e.$store.state.user.newPath),e.$router.push("/".concat(h))}}))})).catch((function(){e.loading=!1}));case 8:case"end":return o.stop()}}),o)})))()}}},c=d,s=t("2877"),v=Object(s["a"])(c,u,r,!1,null,null,null);o["default"]=v.exports}}]);