<!DOCTYPE html><html><head><meta charset=utf-8><meta http-equiv=X-UA-Compatible content="IE=edge,chrome=1"><meta name=renderer content=webkit><meta name=viewport content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel=icon href="" id=dynamic-favicon><title>舆情监测系统</title><meta property=og:title content=舆情监测系统><script src="https://webapi.amap.com/maps?v=1.3&key=b13ee2a98ae5b3cdf1b37a61828f6f9d&plugin=AMap.DistrictSearch"></script><script src=https://webapi.amap.com/ui/1.0/main.js></script><!--[if lt IE 11]><script>window.location.href='html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }

    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }

      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #e9f8fa;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }

    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      /* font-family: 'Open Sans'; */
      color: rgb(95, 140, 231);
      font-size: 20px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 50%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #fff;
      opacity: 0.5;
    }</style><link href=/static/css/chunk-libs.b57e4bfb.css rel=stylesheet><link href=/static/css/app.4a6dbe02.css rel=stylesheet></head><body><script src=/tagcanvas.min.js></script><div id=app><div id=loader-wrapper><div class="loader-section section-left"></div><div class="loader-section section-right"></div><div class=load_title>Loading ...</div></div></div><script>(function(c){function e(e){for(var u,d,t=e[0],f=e[1],r=e[2],k=0,o=[];k<t.length;k++)d=t[k],Object.prototype.hasOwnProperty.call(a,d)&&a[d]&&o.push(a[d][0]),a[d]=0;for(u in f)Object.prototype.hasOwnProperty.call(f,u)&&(c[u]=f[u]);b&&b(e);while(o.length)o.shift()();return h.push.apply(h,r||[]),n()}function n(){for(var c,e=0;e<h.length;e++){for(var n=h[e],u=!0,d=1;d<n.length;d++){var t=n[d];0!==a[t]&&(u=!1)}u&&(h.splice(e--,1),c=f(f.s=n[0]))}return c}var u={},d={runtime:0},a={runtime:0},h=[];function t(c){return f.p+"static/js/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-07dcd4fa":"ae8f12a0","chunk-15090bf1":"423e3060","chunk-c64da886":"7d1af6c5","chunk-1882fba8":"6d45fc44","chunk-18a533e8":"e6ea8703","chunk-1cd0d916":"6120989f","chunk-208d53c4":"69f5bf27","chunk-5ecb56a0":"a85c3664","chunk-5528270d":"8aaf8799","chunk-211e9752":"0d0da981","chunk-2d0df025":"641a7b95","chunk-05427efb":"73e0eedd","chunk-b0445c6a":"0c1301eb","chunk-4d7dab7a":"953ee455","chunk-2d212b99":"eadf8bea","chunk-1b925f0a":"92fe6cec","chunk-4b217d3a":"6c67a8f9","chunk-54d8c200":"83d8a42d","chunk-da9ff978":"07b0074a","chunk-44e7b4d3":"9d7f74eb","chunk-6f3a5038":"76618c37","chunk-2d0a2db2":"b18b57e6","chunk-2d0ac3da":"a07dcf7c","chunk-2d0c7b50":"a6a11ade","chunk-2d0e2366":"9b139797","chunk-67961041":"8adbdcfe","chunk-2d0f012d":"ddeb06bb","chunk-ead28086":"0f5ca56a","chunk-30ba103a":"9691cd8f","chunk-5545fd80":"abf8febd","chunk-5bb73842":"5f889adb","chunk-aab5050c":"d74cd014","chunk-34833a16":"68f78d90","chunk-d860f104":"186f174d","chunk-6c992b88":"a1510058","chunk-c95e6d12":"cc14b0cc","chunk-commons":"06aed53d","chunk-637f8d84":"49ea1986","chunk-37fb4878":"9563aad4","chunk-2e98ed84":"48369573","chunk-e28d6b1a":"284edd54","chunk-ed8af596":"a90868ec","chunk-6c0ff4d2":"5518f842","chunk-2707196a":"37c38631","chunk-21b37318":"53cc16a9","chunk-feccd6d6":"f0c63f0e","chunk-d19c1a98":"9a63e67c","chunk-e94f93c0":"5eb4784c"}[c]+".js"}function f(e){if(u[e])return u[e].exports;var n=u[e]={i:e,l:!1,exports:{}};return c[e].call(n.exports,n,n.exports,f),n.l=!0,n.exports}f.e=function(c){var e=[],n={"chunk-07dcd4fa":1,"chunk-c64da886":1,"chunk-1882fba8":1,"chunk-18a533e8":1,"chunk-1cd0d916":1,"chunk-5528270d":1,"chunk-05427efb":1,"chunk-4d7dab7a":1,"chunk-4b217d3a":1,"chunk-54d8c200":1,"chunk-da9ff978":1,"chunk-44e7b4d3":1,"chunk-6f3a5038":1,"chunk-67961041":1,"chunk-ead28086":1,"chunk-30ba103a":1,"chunk-5545fd80":1,"chunk-5bb73842":1,"chunk-34833a16":1,"chunk-d860f104":1,"chunk-6c992b88":1,"chunk-c95e6d12":1,"chunk-commons":1,"chunk-37fb4878":1,"chunk-2e98ed84":1,"chunk-e28d6b1a":1,"chunk-ed8af596":1,"chunk-2707196a":1,"chunk-21b37318":1,"chunk-feccd6d6":1,"chunk-e94f93c0":1};d[c]?e.push(d[c]):0!==d[c]&&n[c]&&e.push(d[c]=new Promise((function(e,n){for(var u="static/css/"+({"chunk-commons":"chunk-commons"}[c]||c)+"."+{"chunk-07dcd4fa":"dcb0e8da","chunk-15090bf1":"31d6cfe0","chunk-c64da886":"f31de525","chunk-1882fba8":"41a32968","chunk-18a533e8":"31c65064","chunk-1cd0d916":"5d02ade1","chunk-208d53c4":"31d6cfe0","chunk-5ecb56a0":"31d6cfe0","chunk-5528270d":"ef5e0793","chunk-211e9752":"31d6cfe0","chunk-2d0df025":"31d6cfe0","chunk-05427efb":"08e60ad6","chunk-b0445c6a":"31d6cfe0","chunk-4d7dab7a":"3a5765fd","chunk-2d212b99":"31d6cfe0","chunk-1b925f0a":"31d6cfe0","chunk-4b217d3a":"17fc9ab6","chunk-54d8c200":"bc16dfe6","chunk-da9ff978":"b615bed2","chunk-44e7b4d3":"11b383d6","chunk-6f3a5038":"cf62e55c","chunk-2d0a2db2":"31d6cfe0","chunk-2d0ac3da":"31d6cfe0","chunk-2d0c7b50":"31d6cfe0","chunk-2d0e2366":"31d6cfe0","chunk-67961041":"fd661bc3","chunk-2d0f012d":"31d6cfe0","chunk-ead28086":"b6694b1b","chunk-30ba103a":"bf952d1c","chunk-5545fd80":"c2c87378","chunk-5bb73842":"84f98409","chunk-aab5050c":"31d6cfe0","chunk-34833a16":"a31aa2c5","chunk-d860f104":"ac8f6de1","chunk-6c992b88":"fb9bd69d","chunk-c95e6d12":"61704d3e","chunk-commons":"58977c59","chunk-637f8d84":"31d6cfe0","chunk-37fb4878":"893ebb1c","chunk-2e98ed84":"ea149205","chunk-e28d6b1a":"92c0f3c6","chunk-ed8af596":"8c4a9bd2","chunk-6c0ff4d2":"31d6cfe0","chunk-2707196a":"7c2dcefa","chunk-21b37318":"dd9a003d","chunk-feccd6d6":"2386429f","chunk-d19c1a98":"31d6cfe0","chunk-e94f93c0":"bc8d1b03"}[c]+".css",a=f.p+u,h=document.getElementsByTagName("link"),t=0;t<h.length;t++){var r=h[t],k=r.getAttribute("data-href")||r.getAttribute("href");if("stylesheet"===r.rel&&(k===u||k===a))return e()}var o=document.getElementsByTagName("style");for(t=0;t<o.length;t++){r=o[t],k=r.getAttribute("data-href");if(k===u||k===a)return e()}var b=document.createElement("link");b.rel="stylesheet",b.type="text/css",b.onload=e,b.onerror=function(e){var u=e&&e.target&&e.target.src||a,h=new Error("Loading CSS chunk "+c+" failed.\n("+u+")");h.code="CSS_CHUNK_LOAD_FAILED",h.request=u,delete d[c],b.parentNode.removeChild(b),n(h)},b.href=a;var i=document.getElementsByTagName("head")[0];i.appendChild(b)})).then((function(){d[c]=0})));var u=a[c];if(0!==u)if(u)e.push(u[2]);else{var h=new Promise((function(e,n){u=a[c]=[e,n]}));e.push(u[2]=h);var r,k=document.createElement("script");k.charset="utf-8",k.timeout=120,f.nc&&k.setAttribute("nonce",f.nc),k.src=t(c);var o=new Error;r=function(e){k.onerror=k.onload=null,clearTimeout(b);var n=a[c];if(0!==n){if(n){var u=e&&("load"===e.type?"missing":e.type),d=e&&e.target&&e.target.src;o.message="Loading chunk "+c+" failed.\n("+u+": "+d+")",o.name="ChunkLoadError",o.type=u,o.request=d,n[1](o)}a[c]=void 0}};var b=setTimeout((function(){r({type:"timeout",target:k})}),12e4);k.onerror=k.onload=r,document.head.appendChild(k)}return Promise.all(e)},f.m=c,f.c=u,f.d=function(c,e,n){f.o(c,e)||Object.defineProperty(c,e,{enumerable:!0,get:n})},f.r=function(c){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(c,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(c,"__esModule",{value:!0})},f.t=function(c,e){if(1&e&&(c=f(c)),8&e)return c;if(4&e&&"object"===typeof c&&c&&c.__esModule)return c;var n=Object.create(null);if(f.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:c}),2&e&&"string"!=typeof c)for(var u in c)f.d(n,u,function(e){return c[e]}.bind(null,u));return n},f.n=function(c){var e=c&&c.__esModule?function(){return c["default"]}:function(){return c};return f.d(e,"a",e),e},f.o=function(c,e){return Object.prototype.hasOwnProperty.call(c,e)},f.p="/",f.oe=function(c){throw console.error(c),c};var r=window["webpackJsonp"]=window["webpackJsonp"]||[],k=r.push.bind(r);r.push=e,r=r.slice();for(var o=0;o<r.length;o++)e(r[o]);var b=k;n()})([]);</script><script src=/static/js/chunk-elementUI.6ffd8bbd.js></script><script src=/static/js/chunk-libs.ddf357aa.js></script><script src=/static/js/app.5db3fbf4.js></script></body></html>