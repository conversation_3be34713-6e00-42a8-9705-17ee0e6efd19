(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-feccd6d6"],{7411:function(e,t,n){},"8fd1":function(e,t,n){"use strict";n("7411")},a3a9:function(e,t,n){"use strict";n.r(t);var r=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"vsConsolePage"},[n("VsScreen",{staticStyle:{height:"calc(100% - 0px)"},attrs:{drillTaskId:e.drillTaskId,"current-step":e.currentStep,"next-step":e.nextStep,newCurrentStage:e.newCurrentStage,detailInfo:e.detailInfo,showInstruction:!1,loading:e.vsScreenLoading}})],1)},a=[],s=(n("4de4"),n("e9c4"),n("b64b"),n("d3b7"),n("0643"),n("2382"),n("986e")),i=n("265d"),d=n("f994"),o={components:{VsScreen:d["default"]},data:function(){return{currentStep:"",nextStep:"",newCurrentStage:{blueStageScore:"",redStageScore:"",scoreType:""},detailInfo:{},vsScreenLoading:!1}},computed:{drillTaskId:function(){var e;return(null===(e=this.$route.query)||void 0===e?void 0:e.drillTaskId)||""}},created:function(){this.unlistenMessage=Object(s["c"])(s["a"].MESSAGE,this.handleMessage)},beforeDestroy:function(){var e;null===(e=this.unlistenMessage)||void 0===e||e.call(this)},mounted:function(){var e;null!==(e=this.$route.query)&&void 0!==e&&e.drillTaskId?this.queryDetailInfo():this.$message.error("id不能为空")},methods:{queryDetailInfo:function(){var e=this;this.vsScreenLoading=!0,Object(i["h"])({drillTaskId:this.drillTaskId}).then((function(t){if("200"==t.code){console.log("detailInfo",t.data),e.detailInfo=t.data,e.currentStep=t.data.currentStageId,e.nextStep=t.data.nextStageId;var n=t.data.drillProcessStageRes.filter((function(e){return e.processStageId==t.data.currentStageId}));n.length>0?e.newCurrentStage=JSON.parse(JSON.stringify(n))[0]:e.newCurrentStage={},console.log("currentStep1",e.currentStep,"nextStep1",e.nextStep,"newCurrentStage",e.newCurrentStage)}})).catch((function(t){console.error("获取演练详情失败:",t),e.$message.error("获取演练详情失败，请重试")})).finally((function(){e.vsScreenLoading=!1}))},handleMessage:function(e){var t=e.channel,n=e.data;if("DRILL_STAGE"===t){var r=n.drillTaskId,a=n.currentStageId,s=n.nextStageId,i=n.newCurrentStage;if(r!=this.drillTaskId)return console.log("drillTaskId不一致");this.currentStep=a,this.nextStep=s,this.newCurrentStage=i,this.queryDetailInfo()}}}},c=o,l=(n("8fd1"),n("2877")),u=Object(l["a"])(c,r,a,!1,null,"9e128cbe",null);t["default"]=u.exports}}]);